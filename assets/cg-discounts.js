/**
 * 商品折扣叠加系统
 *
 * 核心功能：
 * 1. 通过 Shopline API 获取商品自动折扣活动信息
 * 2. 在当前售价基础上叠加 API 折扣（非原价）
 * 3. 使用 MutationObserver 监控 DOM 变化，自动处理新增商品
 * 4. 支持产品卡片和产品详情页两种页面类型
 */

const productInfoMap = new Map();
let globalDiscountData = null;

const CONFIG = {
  API_URL: "https://caguuu.myshopline.com/storefront/graph/v20250601/graphql.json",
  API_TOKEN: "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ5c291bCIsInRva2VuIjoiZGFlNWZmZTk0OGNiMjIwNGYzYzNjNTlkZjgwMGIzY2QzYWUyMGU1Y3w4ODUzNXwxNzI2NDY0MTkyNDI3fDE3NDY3NzE2NjB8MjA2MTU1Nzk2MnwifQ.eYX30QQX4a-hCvA8u_yhfMLzsCvn7DwzkFvLPeZZkSu05HlwuUTA4vXJ2DHpuDinVbLvM-mHeGgV0tUcbXW8Kg",

  SELECTORS: {
    BADGE: '.card__badge',
    PRODUCT_CARD: '[data-product-id]',
    PRICE_SALE: '.price-item--sale',
    PRICE_REGULAR: '.price-item--regular',
    PRICE_CONTAINER: '.price__container',
    PRICE_SAVE: '.price-item--save',
    PRODUCT_DETAIL: 'main-product-detail'
  },

  CURRENCY: {
    SYMBOL: '¥',
    LOCALE: 'ja-JP'
  }
};

function detectPageType() {
  const productDetailElement = document.querySelector(CONFIG.SELECTORS.PRODUCT_DETAIL);
  return productDetailElement ? 'product-detail' : 'product-list';
}

async function getBasicProductDiscounts(productId) {
  const headers = {
    'content-type': 'application/json',
    'authorization': CONFIG.API_TOKEN
  };

  const body = {
    query: `query GetBasicProductDiscounts($p1: ProductDiscountsInput!) {
  p1: productDiscounts(productDiscountsInput: $p1) {
    productId
    autoDiscountActivities {
      activityName
      activitySeq
      startsAt
      endsAt
      benefitConditions {
        benefit {
          discount
          promotionSeq
        }
        benefitEvent {
          minThreshold
        }
      }
    }
  }
}`,
    variables: {
      p1: {
        discountsProduct: {
          productId: `gid://shopline/Product/${productId}`,
        },
      },
    },
    operationName: "GetBasicProductDiscounts",
  };

  try {
    const response = await fetch(CONFIG.API_URL, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const extractedData = extractDiscountData(data);
    return extractedData;
  } catch (error) {
    console.error("Error fetching product discounts:", error);

    return {
      productId,
      discounts: [],
      summary: {
        totalActivities: 0,
        bestDiscount: 0
      },
      error: error.message
    };
  }
}

function extractDiscountData(responseData) {
  const p1 = responseData.data?.p1;
  if (!p1 || !p1.autoDiscountActivities) {
    return { discounts: [], productId: null };
  }

  const productId = p1.productId;
  const activities = p1.autoDiscountActivities;

  const extractedDiscounts = activities.map(activity => {
    const discountTiers = activity.benefitConditions.map(condition => ({
      minThreshold: parseInt(condition.benefitEvent.minThreshold),
      minThresholdText: parseInt(condition.benefitEvent.minThreshold) / 100,
      discount: condition.benefit.discount,
      discountText: `${100 - condition.benefit.discount}% OFF`
    })).sort((a, b) => a.minThreshold - b.minThreshold);

    return {
      activityName: activity.activityName,
      activitySeq: activity.activitySeq,
      discountStyleText: activity.discountStyleText,
      discountTiers: discountTiers
    };
  });

  return {
    productId,
    discounts: extractedDiscounts,
    summary: {
      totalActivities: activities.length,
      bestDiscount: Math.max(...activities.flatMap(a =>
        a.benefitConditions.map(c => c.benefit.discount)
      ))
    }
  };
}

// 从 DOM 中提取商品信息，适配产品卡片和产品详情页两种结构
function parseProductInfoFromDOM(productCard) {
  const productId = productCard.getAttribute('data-product-id');
  if (!productId) {
    console.warn('Product card missing data-product-id attribute');
    return null;
  }

  const pageType = detectPageType();

  const titleElement = productCard.querySelector('.product__title');
  const title = titleElement ? titleElement.textContent.trim() : '';

  const priceContainer = productCard.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER);
  let originalPrice = 0;
  let salePrice = 0;
  let currentDiscount = 0;

  if (priceContainer) {
    const salePriceElement = priceContainer.querySelector(CONFIG.SELECTORS.PRICE_SALE);
    if (salePriceElement) {
      const salePriceText = salePriceElement.textContent.replace(/[^\d,]/g, '').replace(/,/g, '');
      salePrice = parseInt(salePriceText) || 0;
    }

    const regularPriceElement = priceContainer.querySelector(CONFIG.SELECTORS.PRICE_REGULAR);
    if (regularPriceElement) {
      const regularPriceText = regularPriceElement.textContent.replace(/[^\d,]/g, '').replace(/,/g, '');
      originalPrice = parseInt(regularPriceText) || 0;
    }
  }

  //如果页面没有显示原价，说明商品未打折，将售价作为原价处理
  if (originalPrice === 0 && salePrice > 0) {
    originalPrice = salePrice;
  }

  if (originalPrice > 0 && salePrice > 0 && salePrice < originalPrice) {
    currentDiscount = Math.round((1 - salePrice / originalPrice) * 100);
  }

  const badgeElement = productCard.querySelector(CONFIG.SELECTORS.BADGE);
  let badgeText = '';
  if (badgeElement) {
    badgeText = badgeElement.textContent.trim();
  }

  const saveElement = priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SAVE);

  return {
    productId,
    title,
    originalPrice,
    salePrice,
    currentDiscount,
    badgeText,
    pageType,
    domElements: {
      productCard,
      priceContainer,
      badgeElement,
      saveElement,
      salePriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SALE),
      regularPriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_REGULAR)
    }
  };
}

// 核心折扣计算逻辑：API 折扣叠加在当前售价基础上，而非原价
function calculateDiscountedPrices(productInfo, discountData) {
  if (!discountData.discounts || discountData.discounts.length === 0) {
    console.log(`Product ID ${productInfo.productId}: Best applicable API discount tier:`, appliedTier);
    return {
      finalSalePrice: productInfo.salePrice,
      finalOriginalPrice: productInfo.originalPrice,
      finalDiscount: productInfo.currentDiscount,
      appliedDiscounts: [],
      hasApiDiscount: false
    };
  }

  let bestApiDiscount = 0;
  let appliedDiscounts = [];
  let appliedTier = null;

  const productPrice = productInfo.salePrice;
  console.log(`Calculating API discount for Product ID ${productInfo.productId}. Current sale price: <span class="math-inline">\{formatPriceHalfYen\(productPrice\)\} \(</span>{productPrice})`);

  // 查找适用的最佳折扣梯度
  discountData.discounts.forEach(discount => {
    const sortedTiers = [...discount.discountTiers].sort((a, b) => b.minThreshold - a.minThreshold);

    // minThreshold 以分为单位，需要转换商品价格进行比较
    const productPriceInCents = productPrice * 100;

    for (const tier of sortedTiers) {
      if (productPriceInCents >= tier.minThreshold) {
        // API返回的discount字段是保留比例(如85表示85%)，需转换为折扣比例(15%)
        const discountPercent = 100 - tier.discount;
        if (discountPercent > bestApiDiscount) {
          bestApiDiscount = discountPercent;
          appliedTier = tier;
          appliedDiscounts = [{
            activityName: discount.activityName,
            discountPercent: discountPercent,
            discountText: tier.discountText,
            minThreshold: tier.minThreshold,
            minThresholdText: tier.minThresholdText
          }];
        }
        // 找到第一个满足条件的梯度后立即跳出，避免被低门槛梯度覆盖
        break;
      }
    }
  });

  // 关键：在当前售价基础上应用 API 折扣，然后计算相对于原价的总折扣率
  let finalSalePrice = productInfo.salePrice;
  let hasApiDiscount = false;

  if (bestApiDiscount > 0 && appliedTier) {
    // 在当前售价基础上应用 API 折扣，向下取整
    finalSalePrice = Math.floor(productInfo.salePrice * (100 - bestApiDiscount) / 100);
    hasApiDiscount = true;

    console.log(`API discount applied: ${productInfo.salePrice} * (1 - ${bestApiDiscount}%) = ${finalSalePrice} (min threshold: ${appliedTier.minThresholdText}円)`);
  } else {
    console.log(`No applicable discount for product price ${productInfo.salePrice} (${formatPriceHalfYen(productInfo.salePrice)})`);
  }

  // 计算相对于绝对原价的总折扣率
  let finalDiscount = 0;
  if (productInfo.originalPrice > 0 && finalSalePrice < productInfo.originalPrice) {
    finalDiscount = Math.round((productInfo.originalPrice - finalSalePrice) / productInfo.originalPrice * 100);
  }

  return {
    finalSalePrice,
    finalOriginalPrice: productInfo.originalPrice,
    finalDiscount,
    appliedDiscounts,
    hasApiDiscount,
    debugInfo: {
      originalPrice: productInfo.originalPrice,
      currentSalePrice: productInfo.salePrice,
      currentDiscount: productInfo.currentDiscount,
      apiDiscountPercent: bestApiDiscount,
      appliedThreshold: appliedTier ? appliedTier.minThreshold : null,
      finalSalePrice,
      totalDiscountPercent: finalDiscount
    }
  };
}

function formatPriceHalfYen(price) {
  const halfYen = '\u00A5'; // 半角日圆符号
  const nf = new Intl.NumberFormat('ja-JP', {
    style: 'currency',
    currency: 'JPY',
    minimumFractionDigits: 0
  });

  // 拆分为若干部分，然后把 currency 部分替换
  return nf
    .formatToParts(price)
    .map(part => part.type === 'currency' ? halfYen : part.value)
    .join('');
}

// 更新 DOM 显示，适配产品卡片和产品详情页的不同结构
function updateProductCardDisplay(productInfo, priceData) {
  // 若既没有原始折扣，也没有 API 叠加折扣 → 不改 DOM
  if (productInfo.currentDiscount === 0) {
    return;
  }
  const { domElements, pageType } = productInfo;

  try {
    if (domElements.salePriceElement) {
      const formattedSalePrice = formatPriceHalfYen(priceData.finalSalePrice);
      const taxInfo = domElements.salePriceElement.querySelector('p');

      if (pageType === 'product-detail') {
        domElements.salePriceElement.innerHTML = `${formattedSalePrice}${taxInfo ? taxInfo.outerHTML : ''}`;
      } else {
        // 非商品详情页面商品卡片价格后需要添加"~"符号，表示起始价格
        domElements.salePriceElement.innerHTML = `${formattedSalePrice}${taxInfo ? taxInfo.outerHTML : ''} ~`;
      }
    }

    if (domElements.regularPriceElement && priceData.finalSalePrice < priceData.finalOriginalPrice) {
      const formattedOriginalPrice = formatPriceHalfYen(priceData.finalOriginalPrice);
      domElements.regularPriceElement.textContent = formattedOriginalPrice;
    }

    // 根据页面类型更新折扣显示
    if (pageType === 'product-detail') {
      if (domElements.saveElement && priceData.finalDiscount > 0) {
        domElements.saveElement.textContent = `${priceData.finalDiscount}%OFF`;
      }
    } else {
      if (domElements.badgeElement && priceData.finalDiscount > 0) {
        const badgeSpan = domElements.badgeElement.querySelector('span');
        if (badgeSpan) {
          badgeSpan.textContent = `${priceData.finalDiscount}％OFF`;
        }
      }
    }

    productInfo.domElements.productCard?.setAttribute('data-discount-updated', '1');

    console.log(`Updated ${pageType} product ${productInfo.productId}: ${formatPriceHalfYen(priceData.finalSalePrice)} (${priceData.finalDiscount}% OFF)`);
  } catch (error) {
    console.error('Error updating product card display:', error);
  }
}

// 处理单个商品的折扣更新，支持缓存复用和轮播/无限滚动场景
async function processProductDiscount(productCard) {
  try {
    const productInfo = parseProductInfoFromDOM(productCard);
    if (!productInfo) return;
    console.log(`Processing product ${productInfo.productId} (Page type: ${productInfo.pageType})...`);

    const cached = productInfoMap.get(productInfo.productId);

    // 如果已有缓存且已计算过 API 折扣，直接复用结果
    if (cached && cached.hasApiDiscount) {
      if (!productCard.hasAttribute('data-discount-updated')) {
        const priceData = {
          finalSalePrice: cached.finalSalePrice,
          finalOriginalPrice: cached.finalOriginalPrice,
          finalDiscount: cached.finalDiscount,
          appliedDiscounts: cached.appliedDiscounts,
          hasApiDiscount: true
        };

        const priceContainer = productCard.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER);
        const domElements = {
          productCard,
          priceContainer,
          badgeElement: productCard.querySelector(CONFIG.SELECTORS.BADGE),
          saveElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SAVE),
          salePriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SALE),
          regularPriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_REGULAR)
        };

        updateProductCardDisplay({ ...productInfo, domElements }, priceData);
        productCard.setAttribute('data-discount-updated', '1');
      }
      return;
    }

    // 首次计算或需要重新计算
    const priceData = calculateDiscountedPrices(productInfo, globalDiscountData);

    // 批量更新所有同款商品卡片，解决轮播组件克隆多个相同商品的问题
    // 使用:not([data-discount-updated])避免重复处理已更新的卡片
    document
      .querySelectorAll(`[data-product-id="${productInfo.productId}"]:not([data-discount-updated])`)
      .forEach(card => {
        const priceContainer = card.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER);
        const domElements = {
          productCard: card,
          priceContainer,
          badgeElement: card.querySelector(CONFIG.SELECTORS.BADGE),
          saveElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SAVE),
          salePriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SALE),
          regularPriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_REGULAR)
        };
        updateProductCardDisplay({ ...productInfo, domElements }, priceData);
        card.setAttribute('data-discount-updated', '1');
      });

    // 缓存结果供后续复用
    productInfoMap.set(productInfo.productId, {
      ...productInfo,
      ...priceData,
      lastUpdated: Date.now()
    });

    console.log(
      `Applied ${priceData.hasApiDiscount ? 'API' : 'existing'} discount to product ${productInfo.productId}`
    );
  } catch (err) {
    console.error('Error processing product discount:', err);
  }
}

function findProductCard(element) {
  let current = element;
  while (current && current !== document.body) {
    if (current.hasAttribute && current.hasAttribute('data-product-id')) {
      return current;
    }
    current = current.parentElement;
  }
  return null;
}

function handleBadgeChange(badgeElement) {
  const productCard = findProductCard(badgeElement);
  if (productCard) {
    processProductDiscount(productCard);
  }
}

function handlePriceChange(priceElement) {
  const productCard = findProductCard(priceElement);
  if (productCard) {
    processProductDiscount(productCard);
  }
}

// MutationObserver 回调：监控 DOM 变化，自动处理新增商品
function handleMutations(mutations) {
  // 使用Set防止同一个商品卡片在一次mutation中被重复处理
  const processedCards = new Set();

  mutations.forEach(mutation => {
    if (mutation.type === 'childList') {
      mutation.addedNodes.forEach(node => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          if (node.classList && node.classList.contains('card__badge')) {
            handleBadgeChange(node);
          }

          const badgeElements = node.querySelectorAll && node.querySelectorAll(CONFIG.SELECTORS.BADGE);
          if (badgeElements) {
            badgeElements.forEach(badge => {
              const productCard = findProductCard(badge);
              if (productCard && !processedCards.has(productCard)) {
                processedCards.add(productCard);
                handleBadgeChange(badge);
              }
            });
          }

          const priceContainers = node.querySelectorAll && node.querySelectorAll(CONFIG.SELECTORS.PRICE_CONTAINER);
          if (priceContainers) {
            priceContainers.forEach(priceContainer => {
              const productCard = findProductCard(priceContainer);
              if (productCard && !processedCards.has(productCard)) {
                processedCards.add(productCard);
                handlePriceChange(priceContainer);
              }
            });
          }
        }
      });
    }

    if (mutation.type === 'characterData' || mutation.type === 'childList') {
      const target = mutation.target;

      let badgeElement = null;
      if (target.classList && target.classList.contains('card__badge')) {
        badgeElement = target;
      } else {
        badgeElement = target.closest && target.closest(CONFIG.SELECTORS.BADGE);
      }

      if (badgeElement) {
        const productCard = findProductCard(badgeElement);
        if (productCard && !processedCards.has(productCard)) {
          processedCards.add(productCard);
          handleBadgeChange(badgeElement);
        }
      }

      let priceContainer = null;
      if (target.classList && target.classList.contains('price__container')) {
        priceContainer = target;
      } else {
        priceContainer = target.closest && target.closest(CONFIG.SELECTORS.PRICE_CONTAINER);
      }

      if (priceContainer) {
        const productCard = findProductCard(priceContainer);
        if (productCard && !processedCards.has(productCard)) {
          processedCards.add(productCard);
          handlePriceChange(priceContainer);
        }
      }
    }
  });
}

function initializeMutationObserver() {
  const observer = new MutationObserver(handleMutations);

  const config = {
    childList: true,
    subtree: true,
    characterData: true,
    attributes: false
  };

  observer.observe(document.body, config);

  console.log('MutationObserver initialized for card__badge and price elements');
  return observer;
}

function processExistingProducts() {
  const productCards = document.querySelectorAll(CONFIG.SELECTORS.PRODUCT_CARD);
  const pageType = detectPageType();
  console.log(`Found ${productCards.length} existing product cards on ${pageType} page. Starting initial processing.`);

  productCards.forEach(productCard => {
    if (pageType === 'product-detail') {
      const priceContainer = productCard.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER);
      if (priceContainer) {
        processProductDiscount(productCard);
      }
    } else {
      const badge = productCard.querySelector(CONFIG.SELECTORS.BADGE);
      if (badge) {
        processProductDiscount(productCard);
      }
    }
  });
}

async function initializeDiscountSystem() {
  console.log('Initializing discount system...');

  // 使用固定测试商品ID获取全局折扣配置，所有商品共享同一套折扣规则
  // 这样避免了为每个商品单独请求API，提高性能
  const testProductId = '16069927806303341599314095';
  globalDiscountData = await getBasicProductDiscounts(testProductId);
  console.log('Global discount data fetched:', globalDiscountData);

  processExistingProducts();
  const observer = initializeMutationObserver();

  return observer;
}

let discountObserver = null;

function stopDiscountSystem() {
  if (discountObserver) {
    discountObserver.disconnect();
    discountObserver = null;
    console.log('Discount system stopped');
  }
}

async function restartDiscountSystem() {
  stopDiscountSystem();
  discountObserver = await initializeDiscountSystem();
}

function clearProductCache() {
  productInfoMap.clear();
  console.log('Product cache cleared');
}

function forceReprocessAllProducts() {
  clearProductCache();
  processExistingProducts();
  console.log('All products reprocessed');
}

// 页面加载完成后自动初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', async () => {
    discountObserver = await initializeDiscountSystem();
  });
} else {
  (async () => {
    discountObserver = await initializeDiscountSystem();
  })();
}

// 暴露调试接口
window.DiscountSystem = {
  init: async () => await initializeDiscountSystem(),
  stop: stopDiscountSystem,
  restart: restartDiscountSystem,
  processExisting: processExistingProducts,
  clearCache: clearProductCache,
  forceReprocess: forceReprocessAllProducts,
  productMap: productInfoMap,
  config: CONFIG,
  getProductInfo: (productId) => productInfoMap.get(productId),
  getAllProducts: () => Array.from(productInfoMap.entries()),
  getStats: () => ({
    totalProducts: productInfoMap.size,
    processedWithApiDiscount: Array.from(productInfoMap.values()).filter(p => p.hasApiDiscount).length
  })
};

console.log('Discount system loaded. Use window.DiscountSystem for debugging.');